import { getToken } from '@/utils'
import { resolveResError } from './helpers'
import { useUserStore } from '@/store'

export function reqResolve(config) {
  // 处理不需要token的请求
  if (config.noNeedToken) {
    // 使用oauth2的方式登录，设置content-type和body
    if (config.isLoginRequest) {
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded'
      if (config.data && typeof config.data === 'object') {
        const params = new URLSearchParams()
        params.append('username', config.data.username)
        params.append('password', config.data.password)
        params.append('captcha', config.data.captcha) // 用户输入的验证码
        params.append('captcha_key', config.data.captcha_key) // 验证码的唯一标识
        config.data = params
      }
    }

    return config
  }

  const token = getToken()
  if (token) {
    // config.headers.token = config.headers.token || token
    // 使用Bearer认证
    config.headers.Authorization = `Bearer ${token}`
  }

  return config
}

export function reqReject(error) {
  return Promise.reject(error)
}

export function resResolve(response) {
  const { data, status, statusText } = response
  if (data?.code !== 200) {
    const code = data?.code ?? status
    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, data?.msg ?? statusText)
    window.$message?.error(message, { keepAliveOnHover: true })
    return Promise.reject({ code, message, error: data || response })
  }
  return Promise.resolve(data)
}

export async function resReject(error) {
  if (!error || !error.response) {
    const code = error?.code
    /** 根据code处理对应的操作，并返回处理后的message */
    const message = resolveResError(code, error.message)
    window.$message?.error(message)
    return Promise.reject({ code, message, error })
  }
  const { data, status } = error.response

  if (data?.code === 401) {
    try {
      const userStore = useUserStore()
      userStore.logout()
    } catch (error) {
      console.log('resReject error', error)
      return
    }
  }
  // 后端返回的response数据
  const code = data?.code ?? status
  const message = resolveResError(code, data?.msg ?? error.message)
  window.$message?.error(message, { keepAliveOnHover: true })
  return Promise.reject({ code, message, error: error.response?.data || error.response })
}
